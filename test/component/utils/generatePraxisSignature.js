const sha = require('../../../lib/praxis_psp/sha');
const config = require('../../../lib/config')
const pathValue = require('path-value');


// Helper function to generate signature for Praxis notifications
module.exports = (requestBody) => {
  const signatureFields = [
    'merchant_id',
    'application_key',
    'timestamp',
    'customer.customer_token',
    'session.order_id',
    'transaction.tid',
    'transaction.currency',
    'transaction.amount',
    'transaction.conversion_rate',
    'transaction.processed_currency',
    'transaction.processed_amount'
  ];

  const valuesForSignature = signatureFields.map(f => pathValue.resolveValue(requestBody, f));
  return sha.createSignature(valuesForSignature, config.PRAXIS.FSA);
}
