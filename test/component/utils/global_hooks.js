'use strict'

const amqp = require('amqplib')
const rabbitMqConnectionString = 'amqp://guest:guest@rabbitmq:5672/%2f'
const canary = require('./canary')
const db = require('../../../lib/db')

const topology = require('./test_rabbitmq_topology')

const withdrawRequestRequestedBindingKey = 'payment.withdraw-request.requested'
const withdrawRequestReleasedBindingKey = 'payment.withdraw-request.released'
const withdrawRequestRejectedBindingKey = 'payment.withdraw-request.rejected'
const depositCompletedBindingKey = 'payment.deposit.completed'
const refundCompletedBindingKey = 'payment.refund.completed'

const manualBankDepositCompletedBindingKey = 'payment.manual.bank.deposit.completed'
const manualBankWithdrawalCompletedBindingKey = 'payment.manual.bank.withdrawal.completed'
const manualAdjustmentDepositCompleted = 'payment.manual.adjustment.deposit.completed'
const manualAdjustmentWithdrawalCompleted = 'payment.manual.adjustment.withdrawal.completed'
const manualFeeInactiveCompleted = 'payment.manual.fee.inactive.completed'
const automaticFeeInactiveCompleted = 'payment.automatic.fee.inactive.completed'
const manualFeeWithdrawalCompleted = 'payment.manual.fee.withdrawal.completed'
const manualDepositCompletedBindingKey = 'payment.manual.deposit.completed'

const manualAdjustmentIbCommissionDepositCompleted = 'payment.manual.adjustment.ib-commission.deposit.completed'
const manualAdjustmentIbCommissionWithdrawalCompleted = 'payment.manual.adjustment.ib-commission.withdrawal.completed'
const manualAdjustmentBonusDepositCompleted = 'payment.manual.adjustment.bonus.deposit.completed'
const manualAdjustmentBonusWithdrawalCompleted = 'payment.manual.adjustment.bonus.withdrawal.completed'
const manualAdjustmentBonusLosableDepositCompleted = 'payment.manual.adjustment.bonus-losable.deposit.completed'
const manualAdjustmentBonusLosableWithdrawalCompleted = 'payment.manual.adjustment.bonus-losable.withdrawal.completed'
const manualWithdrawalCompleted = 'payment.manual.withdrawal.completed'
const manualCCWithdrawalBsForProfitsCompleted = 'payment.manual.withdrawal.cc-bs-for-profits.completed'
const manualProfitsBankWireWithdrawalCompleted = 'payment.manual.withdrawal.profits-bank-wire.completed'
const manualNonTradingFeesWithdrawalCompleted = 'payment.manual.withdrawal.non-trading-fees.completed'


// Helper function to clean up database before tests
const cleanupDatabase = async () => {
  try {
    console.log('Starting database cleanup...')
    
    // Clean up test data in reverse order of dependencies
    // Use truncate with cascade to handle foreign key constraints
    await db.sequelize.query('TRUNCATE TABLE "Transactions" RESTART IDENTITY CASCADE')
    await db.sequelize.query('TRUNCATE TABLE "Authorizations" RESTART IDENTITY CASCADE') 
    await db.sequelize.query('TRUNCATE TABLE "Verifications" RESTART IDENTITY CASCADE')
    await db.sequelize.query('TRUNCATE TABLE "AccountMappings" RESTART IDENTITY CASCADE')
    
    console.log('Database cleanup completed successfully')
  } catch (error) {
    console.error('Database cleanup failed:', error)
    // Don't throw error to avoid breaking tests - just log it
  }
}

before(function () {
  return cleanupDatabase()
    .then(() => canary())
    .then(() => amqp.connect(rabbitMqConnectionString))
    .then(connection => {
      this.rabbitConnection = connection
      return connection.createChannel()
    })
    .then(channel => {
      this.rabbitChannel = channel

      return Promise.all([
        channel.assertExchange(topology.EXCHANGE.name, topology.EXCHANGE.type, topology.EXCHANGE.options || {}),

        channel.assertQueue(topology.ALL_TEST_QUEUE.name, topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.options),

        channel.assertQueue(topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.name, topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.options),
        channel.assertQueue(topology.RELEASED_WITHDRAWAL_TEST_QUEUE.name, topology.RELEASED_WITHDRAWAL_TEST_QUEUE.options),
        channel.assertQueue(topology.REJECTED_WITHDRAWAL_TEST_QUEUE.name, topology.REJECTED_WITHDRAWAL_TEST_QUEUE.options),
        channel.assertQueue(topology.DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.DEPOSIT_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.REFUND_COMPLETED_TEST_QUEUE.name, topology.REFUND_COMPLETED_TEST_QUEUE.options),

        channel.assertQueue(topology.MANUAL_BANK_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.MANUAL_BANK_DEPOSIT_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_BANK_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_BANK_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_ADJUSTMENT_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_DEPOSIT_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_ADJUSTMENT_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_FEE_INACTIVE_COMPLETED_TEST_QUEUE.name, topology.MANUAL_FEE_INACTIVE_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.AUTOMATIC_FEE_INACTIVE_COMPLETED_TEST_QUEUE.name, topology.AUTOMATIC_FEE_INACTIVE_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_FEE_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_FEE_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.MANUAL_DEPOSIT_COMPLETED_TEST_QUEUE.options),

        channel.assertQueue(topology.MANUAL_ADJUSTMENT_IB_COMMISSION_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_IB_COMMISSION_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_ADJUSTMENT_IB_COMMISSION_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_IB_COMMISSION_DEPOSIT_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_ADJUSTMENT_BONUS_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_BONUS_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_ADJUSTMENT_BONUS_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_BONUS_DEPOSIT_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_ADJUSTMENT_BONUS_LOSABLE_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_BONUS_LOSABLE_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_ADJUSTMENT_BONUS_LOSABLE_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.MANUAL_ADJUSTMENT_BONUS_LOSABLE_DEPOSIT_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_CC_WITHDRAWAL_BS_FOR_PROFIT_COMPLETED_TEST_QUEUE.name, topology.MANUAL_CC_WITHDRAWAL_BS_FOR_PROFIT_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_PROFITS_BANK_WIRE_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_PROFITS_BANK_WIRE_WITHDRAWAL_COMPLETED_TEST_QUEUE.options),
        channel.assertQueue(topology.MANUAL_NON_TRADING_FEES_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.MANUAL_NON_TRADING_FEES_WITHDRAWAL_COMPLETED_TEST_QUEUE.options)
      ])
    })
    .then(() => {
      return Promise.all([
        this.rabbitChannel.bindQueue(topology.ALL_TEST_QUEUE.name, topology.EXCHANGE.name, 'payment.#'),

        this.rabbitChannel.bindQueue(topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.name, topology.EXCHANGE.name, withdrawRequestRequestedBindingKey),
        this.rabbitChannel.bindQueue(topology.RELEASED_WITHDRAWAL_TEST_QUEUE.name, topology.EXCHANGE.name, withdrawRequestReleasedBindingKey),
        this.rabbitChannel.bindQueue(topology.REJECTED_WITHDRAWAL_TEST_QUEUE.name, topology.EXCHANGE.name, withdrawRequestRejectedBindingKey),
        this.rabbitChannel.bindQueue(topology.DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, depositCompletedBindingKey),
        this.rabbitChannel.bindQueue(topology.REFUND_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, refundCompletedBindingKey),

        this.rabbitChannel.bindQueue(topology.MANUAL_BANK_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualBankDepositCompletedBindingKey),
        this.rabbitChannel.bindQueue(topology.MANUAL_BANK_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualBankWithdrawalCompletedBindingKey),
        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentDepositCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentWithdrawalCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_FEE_INACTIVE_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualFeeInactiveCompleted),
        this.rabbitChannel.bindQueue(topology.AUTOMATIC_FEE_INACTIVE_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, automaticFeeInactiveCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_FEE_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualFeeWithdrawalCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualDepositCompletedBindingKey),

        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_IB_COMMISSION_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentIbCommissionWithdrawalCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_IB_COMMISSION_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentIbCommissionDepositCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_BONUS_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentBonusWithdrawalCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_BONUS_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentBonusDepositCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_BONUS_LOSABLE_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentBonusLosableWithdrawalCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_ADJUSTMENT_BONUS_LOSABLE_DEPOSIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualAdjustmentBonusLosableDepositCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualWithdrawalCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_CC_WITHDRAWAL_BS_FOR_PROFIT_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualCCWithdrawalBsForProfitsCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_PROFITS_BANK_WIRE_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualProfitsBankWireWithdrawalCompleted),
        this.rabbitChannel.bindQueue(topology.MANUAL_NON_TRADING_FEES_WITHDRAWAL_COMPLETED_TEST_QUEUE.name, topology.EXCHANGE.name, manualNonTradingFeesWithdrawalCompleted)
      ])
    })
})

after(function () {
  return Promise.all([
    this.rabbitChannel.unbindQueue(topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.name, topology.EXCHANGE.name, 'payment.withdraw-request.requested'),
    this.rabbitChannel.unbindQueue(topology.RELEASED_WITHDRAWAL_TEST_QUEUE.name, topology.EXCHANGE.name, 'payment.withdraw-request.released'),
    this.rabbitChannel.unbindQueue(topology.REJECTED_WITHDRAWAL_TEST_QUEUE.name, topology.EXCHANGE.name, 'payment.withdraw-request.rejected')
  ])
    .then(() => {
      return this.rabbitChannel.close()
    })
    .then(() => this.rabbitConnection.close())
})
